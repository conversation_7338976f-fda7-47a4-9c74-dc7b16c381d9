spring:
  datasource:
    url: ${DATABASE_URL:*****************************************}?stringtype=unspecified
    username: ${DATABASE_USER:postgres}
    password: ${DATABASE_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
  data:
    redis:
      database: ${REDIS_DATABASE:0}
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:redis}
      username: ${REDIS_USERNAME:default}
      timeout: 10s
      lettuce:
        pool:
          max-active: 200
          max-wait: -1ms
          max-idle: 10
          min-idle: 0
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    baseline-version: 0
    encoding: UTF-8
    sql-migration-prefix: V
    sql-migration-separator: __
    sql-migration-suffixes: .sql
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    from: ${MAIL_FROM}
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            require: true
          ssl:
            enable: true

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/**/*.xml

server:
  port: 8080

# OTP验证码相关配置
app:
  otp:
    expiration-time: 5  # 验证码过期时间（分钟）

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: -1
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: 259200
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: true
  isReadCookie: false
  token-name: Authorization
  token-prefix: Bearer
