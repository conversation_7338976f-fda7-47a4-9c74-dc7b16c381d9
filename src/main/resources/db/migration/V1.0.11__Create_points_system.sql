-- 为articles表添加精选状态字段
ALTER TABLE articles
ADD COLUMN is_featured BOOLEAN NOT NULL DEFAULT false;

-- 为新字段创建索引以提高查询性能
CREATE INDEX idx_articles_is_featured ON articles(is_featured);

-- 添加注释
COMMENT ON COLUMN articles.is_featured IS '是否为精选文章，精选文章在积分计算时会获得额外奖励'; 

-- 创建积分系统相关表

-- 创作者积分账户表
CREATE TABLE IF NOT EXISTS creator_points (
    id bigserial PRIMARY KEY,
    user_id text UNIQUE NOT NULL,
    total_points bigint DEFAULT 0 NOT NULL,
    available_points bigint DEFAULT 0 NOT NULL,
    frozen_points bigint DEFAULT 0 NOT NULL,
    withdrawn_points bigint DEFAULT 0 NOT NULL,
    last_calculated_at timestamp DEFAULT now() NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL
);

CREATE INDEX IF NOT EXISTS creator_points_user_id_idx ON creator_points(user_id);
CREATE INDEX IF NOT EXISTS creator_points_available_points_idx ON creator_points(available_points);

-- 积分交易记录表
CREATE TABLE IF NOT EXISTS points_transactions (
    id bigserial PRIMARY KEY,
    transaction_id text UNIQUE NOT NULL,
    user_id text NOT NULL,
    content_id text NULL,
    "type" text NOT NULL,
    sub_type text NULL,
    amount bigint NOT NULL,
    balance bigint NOT NULL,
    description text NULL,
    metadata jsonb NULL,
    created_at timestamp DEFAULT now() NOT NULL
);

CREATE INDEX IF NOT EXISTS points_transactions_user_id_idx ON points_transactions(user_id);
CREATE INDEX IF NOT EXISTS points_transactions_content_id_idx ON points_transactions(content_id);
CREATE INDEX IF NOT EXISTS points_transactions_type_idx ON points_transactions(type);
CREATE INDEX IF NOT EXISTS points_transactions_user_type_time_idx ON points_transactions(user_id, type, created_at DESC);
CREATE INDEX IF NOT EXISTS points_transactions_created_at_idx ON points_transactions(created_at);



-- 管理员手动奖励记录表
CREATE TABLE IF NOT EXISTS manual_bonus_records (
    id bigserial PRIMARY KEY,
    content_id text NOT NULL,
    author_id text NOT NULL,
    admin_id text NOT NULL,
    bonus_points bigint NOT NULL,
    reason text NOT NULL,
    bonus_type text DEFAULT 'quality' NOT NULL,
    status text DEFAULT 'active' NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL
);

CREATE INDEX IF NOT EXISTS manual_bonus_records_content_id_idx ON manual_bonus_records(content_id);
CREATE INDEX IF NOT EXISTS manual_bonus_records_author_id_idx ON manual_bonus_records(author_id);
CREATE INDEX IF NOT EXISTS manual_bonus_records_admin_id_idx ON manual_bonus_records(admin_id);

-- 积分配置表
CREATE TABLE IF NOT EXISTS points_config (
    id bigserial PRIMARY KEY,
    config_key text UNIQUE NOT NULL,
    config_value text NOT NULL,
    description text NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL
);

-- 提现申请表
CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id bigserial PRIMARY KEY,
    request_id text UNIQUE NOT NULL,
    user_id text NOT NULL,
    amount_points bigint NOT NULL,
    amount_usd numeric(10,2) NOT NULL,
    target_currency text NOT NULL,
    target_amount numeric(15,2) NULL,
    status text DEFAULT 'pending' NOT NULL,
    withdrawal_method text DEFAULT 'wise' NOT NULL,
    wise_transfer_id text NULL,
    wise_quote_id text NULL,
    wise_recipient_id bigint NULL,
    wise_status text NULL,
    wise_fee numeric(10,2) NULL,
    wise_exchange_rate numeric(15,6) NULL,
    recipient_info jsonb NOT NULL,
    admin_id text NULL,
    admin_notes text NULL,
    processed_at timestamp NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL
);

CREATE INDEX IF NOT EXISTS withdrawal_requests_user_id_idx ON withdrawal_requests(user_id);
CREATE INDEX IF NOT EXISTS withdrawal_requests_status_idx ON withdrawal_requests(status);
CREATE INDEX IF NOT EXISTS withdrawal_requests_user_status_idx ON withdrawal_requests(user_id, status);
CREATE INDEX IF NOT EXISTS withdrawal_requests_wise_transfer_idx ON withdrawal_requests(wise_transfer_id);
CREATE INDEX IF NOT EXISTS withdrawal_requests_created_at_idx ON withdrawal_requests(created_at);

-- 表注释
COMMENT ON TABLE creator_points IS '创作者积分账户表，记录用户的积分余额和状态';
COMMENT ON COLUMN creator_points.total_points IS '累计总积分（整数）';
COMMENT ON COLUMN creator_points.available_points IS '可提现积分';
COMMENT ON COLUMN creator_points.frozen_points IS '冻结积分';
COMMENT ON COLUMN creator_points.withdrawn_points IS '已提现积分';

COMMENT ON TABLE points_transactions IS '积分交易记录表，记录所有积分变动';
COMMENT ON COLUMN points_transactions.type IS '交易类型：earn获得、withdraw提现、adjust调整、bonus奖励';
COMMENT ON COLUMN points_transactions.sub_type IS '具体类型：like点赞、comment评论、quality_bonus质量奖励等';
COMMENT ON COLUMN points_transactions.amount IS '积分金额（整数）';
COMMENT ON COLUMN points_transactions.balance IS '交易后余额';



COMMENT ON TABLE manual_bonus_records IS '管理员手动奖励记录表';
COMMENT ON COLUMN manual_bonus_records.bonus_type IS '奖励类型：quality质量、special特殊、event活动、correction修正';
COMMENT ON COLUMN manual_bonus_records.status IS '状态：active生效、revoked已撤销';

COMMENT ON TABLE points_config IS '积分配置表，存储系统配置参数';

COMMENT ON TABLE withdrawal_requests IS '提现申请表，记录用户提现申请和处理状态';
COMMENT ON COLUMN withdrawal_requests.status IS '状态：pending待处理、wise_created已创建Wise转账、approved已批准、rejected已拒绝、completed已完成';
COMMENT ON COLUMN withdrawal_requests.recipient_info IS '收款人信息JSON'; 