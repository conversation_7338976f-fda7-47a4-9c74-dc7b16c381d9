CREATE TABLE IF NOT EXISTS polls (
    id BIGSERIAL PRIMARY KEY,
    poll_id TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    author_id TEXT NOT NULL,
    options JSONB NOT NULL,
    poll_type TEXT NOT NULL DEFAULT 'single',
    status TEXT NOT NULL DEFAULT 'active',
    expires_at TIMESTAMPTZ,
    allow_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
    visibility VARCHAR(10) NOT NULL DEFAULT 'public',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_polls_author_id ON polls (author_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_polls_status ON polls (status) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_polls_created_at ON polls (created_at DESC) WHERE deleted_at IS NULL;

CREATE TABLE IF NOT EXISTS poll_votes (
    id BIGSERIAL PRIMARY KEY,
    poll_id TEXT NOT NULL,  -- 改为TEXT保持一致性
    user_id TEXT NOT NULL,  -- 改为TEXT保持一致性
    option_id TEXT NOT NULL,  -- 改为TEXT保持一致性
    voted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT uk_poll_votes_unique UNIQUE (poll_id, user_id, option_id)
);

-- 索引
CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes (poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes (user_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_voted_at ON poll_votes (voted_at DESC);