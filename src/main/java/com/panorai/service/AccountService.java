package com.panorai.service;

import cn.dev33.satoken.secure.BCrypt;
import com.panorai.model.entity.Account;
import com.panorai.repository.AccountRepository;
import com.panorai.utils.IdUtils;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AccountService {
    private final AccountRepository accountRepository;

    public void initPassword(String userId, @Length(min = 8, max = 20) String password) {
        Account existedAccount = getAccount(userId, false, null);
        if (Objects.nonNull(existedAccount)) {
            throw new RuntimeException("password already initialized");
        }
        String hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt(10));
        Account account = new Account();
        account.setId(userId);
        account.setAccountId(IdUtils.generateId());
        account.setUserId(userId);
        account.setProviderId("credential");
        account.setPassword(hashedPassword);
        account.setCreatedAt(Instant.now());
        account.setUpdatedAt(Instant.now());
        accountRepository.save(account);
    }

    public boolean checkPassword(String userId, String password) {
        Account account = getAccount(userId, true, new RuntimeException("Account not found"));
        return BCrypt.checkpw(password, account.getPassword());
    }

    public <T extends RuntimeException> Account getAccount(String userId, boolean throwException, T exception) {
        Optional<Account> accountOpt = accountRepository.optByUserId(userId);
        if (accountOpt.isEmpty()) {
            if (throwException) {
                throw exception;
            } else {
                return null;
            }
        }
        return accountOpt.get();
    }
}