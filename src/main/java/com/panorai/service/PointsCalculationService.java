package com.panorai.service;

import com.panorai.model.dto.ContentMetricsDTO;
import com.panorai.model.entity.Article;

import com.panorai.model.entity.CreatorPoints;
import com.panorai.model.entity.PointsTransaction;
import com.panorai.model.enums.ArticleContentType;
import com.panorai.model.enums.PointsConfigKeys;
import com.panorai.model.enums.PointsTransactionType;
import com.panorai.model.enums.PointsTransactionSubType;
import com.panorai.repository.*;
import com.panorai.utils.IdUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import java.time.temporal.ChronoUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class PointsCalculationService {

    private final CreatorPointsRepository creatorPointsRepository;
    private final PointsTransactionRepository pointsTransactionRepository;
    private final PointsConfigRepository pointsConfigRepository;

    private final ArticleRepository articleRepository;
    private final ArticleCommentRepository articleCommentRepository;
    private final ArticleLikeRepository articleLikeRepository;
    private final ArticleLogRepository articleLogRepository;

    // 缓存积分配置，避免重复查询
    private static final Map<String, String> configCache = new ConcurrentHashMap<>();

    /**
     * 每日凌晨2点执行内容互动积分结算（不包括精选和管理员奖励）
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Transactional
    public void dailyContentInteractionPointsSettlement() {
        log.info("开始执行每日内容互动积分结算任务");
        
        // 1. 刷新积分配置缓存
        refreshConfigCache();

        // 2. 获取需要重新计算的活跃内容（仅包含申请激励的文章）
        List<Article> activeContents = articleRepository.findActiveContentWithOutContent();

        int processedCount = 0;
        int errorCount = 0;

        // 3. 逐个处理内容互动积分计算
        for (Article content : activeContents) {
            try {
                calculateAndUpdateContentInteractionPoints(content);
                processedCount++;
            } catch (Exception e) {
                log.error("内容 {} 互动积分计算失败: {}", content.getArticleId(), e.getMessage(), e);
                errorCount++;
            }
        }

        // 4. 生成结算报告
        generateSettlementReport(processedCount, errorCount);

        log.info("每日内容互动积分结算任务完成，处理: {}, 失败: {}", processedCount, errorCount);
    }

        /**
     * 计算并更新单个内容的互动积分（不包括精选和管理员奖励）
     */
    @Transactional
    public void calculateAndUpdateContentInteractionPoints(Article content) {
        String contentId = content.getArticleId();
        String authorId = content.getAuthorId();
        log.debug("开始计算内容 {} 的互动积分", contentId);

        // 1. 先确定计算截止时间，避免计算过程中新增互动漏算
        Instant calculationEndTime = Instant.now();

        // 2. 获取内容互动数据（基于时间窗口）
        ContentMetricsDTO metrics = getContentMetrics(content, calculationEndTime);
        if (metrics == null) {
            log.warn("无法获取内容 {} 的互动数据", contentId);
            return;
        }

        // 3. 计算新增互动积分（基于增量数据）
        long incrementalPoints = calculateContentInteractionPoints(metrics);
        
        // 4. 只有有新增积分时才更新
        if (incrementalPoints > 0) {
            // 5. 更新用户积分余额（增量）
            updateUserPointsBalance(authorId, contentId, incrementalPoints, PointsTransactionSubType.CONTENT_INTERACTION);
            
            log.info("内容 {} 新增互动积分: {}, 计算截止时间: {}", 
                contentId, incrementalPoints, calculationEndTime);
        } else {
            log.debug("内容 {} 无新增互动", contentId);
        }
    }

    /**
     * 实时处理精选奖励
     */
    @Transactional
    public void processFeaturedBonus(String contentId, String authorId, boolean isFeatured) {
        log.info("处理内容 {} 的精选状态变更: {}", contentId, isFeatured);

        if (isFeatured) {
            // 1. 双重检查：先查询，再在事务中再次检查，防止并发重复发放
            if (hasReceivedFeaturedBonus(contentId)) {
                log.warn("内容 {} 已经获得过精选奖励，跳过", contentId);
                return;
            }

            // 2. 获取文章信息以确定内容类型
            Optional<Article> articleOpt = articleRepository.findByArticleId(contentId);
            if (articleOpt.isEmpty()) {
                log.error("内容 {} 不存在，无法处理精选奖励", contentId);
                throw new IllegalArgumentException("内容不存在: " + contentId);
            }
            
            String contentType = articleOpt.get().getContentType();

            // 3. 事务中再次检查，防止并发问题
            if (hasReceivedFeaturedBonus(contentId)) {
                log.warn("内容 {} 在事务中发现已获得精选奖励，跳过", contentId);
                return;
            }

            // 4. 计算精选奖励
            long featuredBonus = calculateFeaturedBonus(contentType);
            if (featuredBonus <= 0) {
                log.warn("内容 {} 精选奖励计算结果为 {}, 跳过发放", contentId, featuredBonus);
                return;
            }

            // 5. 更新用户积分余额
            updateUserPointsBalance(authorId, contentId, featuredBonus, PointsTransactionSubType.FEATURED_BONUS);

            log.info("精选奖励处理完成，内容: {}, 奖励积分: {}", contentId, featuredBonus);
        } else {
            log.info("内容 {} 取消精选，但不扣除已发放的奖励", contentId);
        }
    }

    /**
     * 实时处理管理员奖励
     */
    @Transactional
    public void processManualBonus(String contentId, String authorId, long bonusAmount, String reason) {
        log.info("处理内容 {} 的管理员奖励: {} 积分，原因: {}", contentId, bonusAmount, reason);

        // 1. 参数验证
        if (contentId == null || contentId.trim().isEmpty()) {
            throw new IllegalArgumentException("内容ID不能为空");
        }
        if (authorId == null || authorId.trim().isEmpty()) {
            throw new IllegalArgumentException("作者ID不能为空");
        }
        if (reason == null || reason.trim().isEmpty()) {
            throw new IllegalArgumentException("奖励原因不能为空");
        }
        
        // 2. 积分数量检查（可以是负数，用于扣除积分，但要有合理限制）
        if (Math.abs(bonusAmount) > 100000) { // 防止异常大的数值
            throw new IllegalArgumentException("奖励积分数量超出合理范围: " + bonusAmount);
        }
        
        if (bonusAmount == 0) {
            log.warn("管理员奖励积分为0，跳过处理");
            return;
        }

        // 3. 验证内容是否存在
        Optional<Article> articleOpt = articleRepository.findByArticleId(contentId);
        if (articleOpt.isEmpty()) {
            throw new IllegalArgumentException("内容不存在: " + contentId);
        }

        // 4. 更新用户积分余额
        updateUserPointsBalance(authorId, contentId, bonusAmount, PointsTransactionSubType.MANUAL_BONUS);

        log.info("管理员奖励处理完成，内容: {}, 奖励: {}, 原因: {}", contentId, bonusAmount, reason);
    }



    /**
     * 获取内容互动数据（基于时间窗口的增量数据）
     */
    private ContentMetricsDTO getContentMetrics(Article content, Instant calculationEndTime) {
        String contentId = content.getArticleId();
        try {
            // 获取上次内容互动积分计算时间（只考虑互动积分，不受精选/管理员奖励影响）
            Instant lastCalculatedAt = getLastContentInteractionCalculatedTime(contentId);
            
            // 统计从上次计算时间到截止时间的新增互动数据
            int newLikeCount = articleLikeRepository.countByArticleIdBetweenTime(contentId, lastCalculatedAt, calculationEndTime);
            int newCommentCount = articleCommentRepository.countByArticleIdBetweenTime(contentId, lastCalculatedAt, calculationEndTime);
            int newViewCount = articleLogRepository.countByArticleIdBetweenTime(contentId, lastCalculatedAt, calculationEndTime);
            // TODO: 需要根据实际分享表实现
            int newShareCount = 0;

            log.debug("内容 {} 时间窗口 [{} -> {}] 新增互动：点赞 {}, 评论 {}, 浏览 {}", 
                contentId, lastCalculatedAt, calculationEndTime, newLikeCount, newCommentCount, newViewCount);

            return ContentMetricsDTO.builder()
                    .contentId(contentId)
                    .authorId(content.getAuthorId())
                    .likeCount(newLikeCount)
                    .commentCount(newCommentCount)
                    .viewCount(newViewCount)
                    .shareCount(newShareCount)
                    .contentType(content.getContentType())
                    .isFeatured(content.getIsFeatured())
                    .build();

        } catch (Exception e) {
            log.error("获取文章 {} 时间窗口互动数据失败", contentId, e);
            return null;
        }
    }

    /**
     * 获取内容的最后互动积分计算时间，只考虑互动积分相关的时间
     * 避免被精选奖励或管理员奖励的时间影响
     */
    private Instant getLastContentInteractionCalculatedTime(String contentId) {
        // 从流水记录中查找最后一次内容互动积分的计算时间
        List<PointsTransaction> lastInteractionTransactions = 
            pointsTransactionRepository.findLastContentInteractionTransaction(contentId);
        
        if (!lastInteractionTransactions.isEmpty()) {
            Instant lastTransactionTime = lastInteractionTransactions.get(0).getCreatedAt();
            // 为避免边界重复计算，返回时间+1毫秒
            Instant adjustedTime = lastTransactionTime.plusMillis(1);
            log.debug("内容 {} 最后互动积分交易时间: {}, 调整后: {}", contentId, lastTransactionTime, adjustedTime);
            return adjustedTime;
        }
        
        // 如果没有流水记录，使用文章创建时间作为起始点
        Optional<Article> articleOpt = articleRepository.findByArticleId(contentId);
        if (articleOpt.isPresent()) {
            Instant articleCreatedAt = articleOpt.get().getCreatedAt();
            log.debug("内容 {} 无历史计算记录，使用文章创建时间: {}", contentId, articleCreatedAt);
            return articleCreatedAt;
        }
        
        // 最后的保底策略：7天前（不应该到这里）
        Instant defaultStartTime = Instant.now().minus(7, ChronoUnit.DAYS);
        log.warn("内容 {} 无法获取文章信息，使用默认起始时间: {}", contentId, defaultStartTime);
        return defaultStartTime;
    }

    /**
     * 计算内容互动积分
     */
    private long calculateContentInteractionPoints(ContentMetricsDTO metrics) {
        // 1. 数据安全检查
        if (metrics.getLikeCount() < 0 || metrics.getCommentCount() < 0 || 
            metrics.getViewCount() < 0 || metrics.getShareCount() < 0) {
            log.error("内容 {} 互动数据异常: 点赞{}, 评论{}, 浏览{}, 分享{}", 
                metrics.getContentId(), metrics.getLikeCount(), metrics.getCommentCount(), 
                metrics.getViewCount(), metrics.getShareCount());
            throw new IllegalStateException("互动数据不能为负数");
        }

        // 2. 获取积分配置
        int pointsPerLike = getConfigInt(PointsConfigKeys.POINTS_PER_LIKE);
        int pointsPerComment = getConfigInt(PointsConfigKeys.POINTS_PER_COMMENT);
        int pointsPerView = getConfigInt(PointsConfigKeys.POINTS_PER_VIEW);
        int pointsPerShare = getConfigInt(PointsConfigKeys.POINTS_PER_SHARE);
        int globalMultiplier = getConfigInt(PointsConfigKeys.GLOBAL_POINTS_MULTIPLIER);

        // 3. 配置安全检查
        if (pointsPerLike < 0 || pointsPerComment < 0 || pointsPerView < 0 || pointsPerShare < 0) {
            log.error("积分配置异常: 点赞{}, 评论{}, 浏览{}, 分享{}", 
                pointsPerLike, pointsPerComment, pointsPerView, pointsPerShare);
            throw new IllegalStateException("积分配置不能为负数");
        }
        
        if (globalMultiplier <= 0 || globalMultiplier > 1000) {
            log.error("全局积分系数异常: {}", globalMultiplier);
            throw new IllegalStateException("全局积分系数必须在0-1000之间");
        }

        // 4. 计算基础积分
        long basePoints = (long) metrics.getLikeCount() * pointsPerLike 
                        + (long) metrics.getCommentCount() * pointsPerComment 
                        + (long) metrics.getViewCount() * pointsPerView 
                        + (long) metrics.getShareCount() * pointsPerShare;

        // 5. 应用全局积分系数
        long adjustedBasePoints = (basePoints * globalMultiplier) / 100;

        // 6. 获取内容类型系数并应用
        double contentTypeMultiplier = getContentTypeMultiplier(metrics.getContentType());
        long finalPoints = (long) (adjustedBasePoints * contentTypeMultiplier);
        
        // 7. 最终安全检查
        if (finalPoints < 0) {
            log.error("内容 {} 计算出负积分: {}, 将设为0", metrics.getContentId(), finalPoints);
            return 0;
        }
        
        return finalPoints;
    }

    /**
     * 计算精选奖励积分
     */
    private long calculateFeaturedBonus(String contentType) {
        int featuredBonus = getConfigInt(PointsConfigKeys.FEATURED_BONUS);
        double contentTypeMultiplier = getContentTypeMultiplier(contentType);
        return (long) (featuredBonus * contentTypeMultiplier);
    }

    /**
     * 检查内容是否已经获得过精选奖励
     */
    private boolean hasReceivedFeaturedBonus(String contentId) {
        List<PointsTransaction> featuredTransactions = 
            pointsTransactionRepository.findByContentIdAndSubType(contentId, PointsTransactionSubType.FEATURED_BONUS.getCode());
        return !featuredTransactions.isEmpty();
    }

    /**
     * 查询内容的总积分
     */
    public long getContentTotalPoints(String contentId) {
        return pointsTransactionRepository.sumPointsByContentId(contentId);
    }

    /**
     * 更新用户积分余额
     */
    private void updateUserPointsBalance(String userId, long deltaPoints, PointsTransactionSubType subType) {
        updateUserPointsBalance(userId, null, deltaPoints, subType);
    }

    /**
     * 更新用户积分余额（包含内容ID）
     * 关键：先记录流水，再更新余额，确保数据一致性
     */
    private void updateUserPointsBalance(String userId, String contentId, long deltaPoints, PointsTransactionSubType subType) {
        if (deltaPoints == 0) {
            return;
        }

        // 获取或创建用户积分账户
        CreatorPoints account = creatorPointsRepository.findByUserId(userId);
        if (account == null) {
            account = createNewCreatorPointsAccount(userId);
        }

        // 计算新余额
        long newTotalPoints = account.getTotalPoints() + deltaPoints;
        long newAvailablePoints = account.getAvailablePoints() + deltaPoints;
        
        // 安全检查：防止余额变成负数
        if (newAvailablePoints < 0) {
            log.error("用户 {} 积分余额不足，当前: {}, 变化: {}", userId, account.getAvailablePoints(), deltaPoints);
            throw new IllegalStateException("积分余额不足，操作被拒绝");
        }

        // 关键：先记录流水（如果失败，整个事务回滚）
        recordPointsTransaction(userId, contentId, deltaPoints, newTotalPoints, subType);

        // 然后更新积分余额
        account.setTotalPoints(newTotalPoints);
        account.setAvailablePoints(newAvailablePoints);
        account.setLastCalculatedAt(Instant.now());
        account.setUpdatedAt(Instant.now());

        creatorPointsRepository.updateById(account);
        
        log.debug("用户 {} 积分更新成功，变化: {}, 新余额: {}", userId, deltaPoints, newTotalPoints);
    }

    /**
     * 创建新的创作者积分账户
     */
    private CreatorPoints createNewCreatorPointsAccount(String userId) {
        CreatorPoints account = new CreatorPoints();
        account.setUserId(userId);
        account.setTotalPoints(0L);
        account.setAvailablePoints(0L);
        account.setFrozenPoints(0L);
        account.setWithdrawnPoints(0L);
        account.setCreatedAt(Instant.now());
        account.setUpdatedAt(Instant.now());
        creatorPointsRepository.save(account);
        return account;
    }

    /**
     * 记录积分交易
     */
    private void recordPointsTransaction(String userId, long amount, long balance, PointsTransactionSubType subType) {
        recordPointsTransaction(userId, null, amount, balance, subType);
    }

    /**
     * 记录积分交易（包含内容ID）
     */
    private void recordPointsTransaction(String userId, String contentId, long amount, long balance, PointsTransactionSubType subType) {
        PointsTransaction transaction = new PointsTransaction();
        transaction.setTransactionId(IdUtils.generateId());
        transaction.setUserId(userId);
        transaction.setContentId(contentId);
        transaction.setType(amount > 0 ? PointsTransactionType.EARN.getCode() : PointsTransactionType.ADJUST.getCode());
        transaction.setSubType(subType.getCode());
        transaction.setAmount(amount);
        transaction.setBalance(balance);
        transaction.setDescription(subType.getDescription());
        transaction.setCreatedAt(Instant.now());

        pointsTransactionRepository.save(transaction);
    }

    /**
     * 获取内容类型积分系数
     */
    private double getContentTypeMultiplier(String contentType) {
        ArticleContentType type = ArticleContentType.fromCode(contentType);

        return switch (type) {
            case ORIGINAL -> getConfigInt(PointsConfigKeys.CONTENT_TYPE_ORIGINAL) / 100.0;
            case DISTRIBUTION -> getConfigInt(PointsConfigKeys.CONTENT_TYPE_DISTRIBUTION) / 100.0;
            case TRANSLATION -> getConfigInt(PointsConfigKeys.CONTENT_TYPE_TRANSLATION) / 100.0;
            default -> 1.0;
        };
    }

    /**
     * 获取配置值（整数）
     */
    private int getConfigInt(PointsConfigKeys configKey) {
        String value = configCache.get(configKey.getKey());
        if (value == null) {
            return configKey.getDefaultValue();
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.warn("配置值 {} 格式错误: {}, 使用默认值: {}", configKey.getKey(), value, configKey.getDefaultValue());
            return configKey.getDefaultValue();
        }
    }

    /**
     * 刷新配置缓存
     */
    private void refreshConfigCache() {
        try {
            Map<String, String> configs = pointsConfigRepository.findAllConfigs();
            configCache.clear();
            configCache.putAll(configs);
            log.debug("积分配置缓存已刷新，共 {} 个配置项", configs.size());
        } catch (Exception e) {
            log.error("刷新积分配置缓存失败", e);
        }
    }

    /**
     * 生成结算报告
     */
    private void generateSettlementReport(int processedCount, int errorCount) {
        int totalCount = processedCount + errorCount;
        double successRate = totalCount > 0 ? (processedCount * 100.0 / totalCount) : 100.0;
        
        log.info("=== 每日内容互动积分结算报告 ===");
        log.info("总内容数: {} 个", totalCount);
        log.info("处理成功: {} 个内容", processedCount);
        log.info("处理失败: {} 个内容", errorCount);
        log.info("成功率: {:.2f}%", successRate);
        log.info("结算时间: {}", LocalDateTime.now());
        log.info("================================");
    }
} 