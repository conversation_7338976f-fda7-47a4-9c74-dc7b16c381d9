package com.panorai.service;

import cn.dev33.satoken.stp.StpUtil;
import com.panorai.model.entity.User;
import com.panorai.model.entity.UserProfile;
import com.panorai.model.enums.OTPType;
import com.panorai.repository.LimitRepository;
import com.panorai.repository.OptRepository;
import com.panorai.utils.RandomStringUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.hibernate.validator.constraints.Length;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AuthService {
    private final LimitRepository limitRepository;
    private final OptRepository optRepository;
    private final EmailService emailService;
    private final UserService userService;
    private final AccountService accountService;

    @SneakyThrows
    public void sendEmailOTP(String email, String ipAddress) {
        checkOptLimit(email, ipAddress);
        String otp = RandomStringUtils.randomNumeric(6);
        optRepository.saveOTP(email, otp);
        limitRepository.setIpLimit(ipAddress);
        limitRepository.setEmailLimit(email);
        emailService.sendOTPEmail(email, otp, OTPType.SIGN_IN);
    }



    public String signInWithOTP(String email, String otp) {
        if (!optRepository.getOTP(email).equals(otp)) {
            throw new RuntimeException("Invalid OTP");
        }
        UserProfile userProfile = userService.checkOrCreateAccount(email);
        optRepository.deleteOTP(email);
        StpUtil.login(userProfile.getUserId(), "web");
        return StpUtil.getTokenValue();
    }

    private void checkOptLimit(String email, String ipAddress) {
        long ttl = limitRepository.checkIpLimit(ipAddress);
        if (ttl > 0) {
            throw new RuntimeException("Too many requests, please try again in " + ttl + " seconds");
        }
        long emailLimit = limitRepository.checkEmailLimit(email);
        if (emailLimit > 0) {
            throw new RuntimeException("Too many requests, please try again in " + emailLimit + " seconds");
        }
    }

    public String signInWithCredential(String email, String password) {
        User user = userService.checkAccount(email);
        String userId = user.getId();
        checkSignInLimit(userId);

        boolean validPassword = accountService.checkPassword(userId, password);
        if (!validPassword) {
            int failCount = limitRepository.incrementSignInFailCount(userId);
            throw new RuntimeException("Invalid password. Attempts: " + failCount + "/5");
        }

        // 登录成功，清除失败记录
        limitRepository.clearSignInFailCount(userId);
        StpUtil.login(userId, "web");
        return StpUtil.getTokenValue();
    }

    private void checkSignInLimit(String userId) {
        long cooldownTtl = limitRepository.getSignInFailCount(userId);
        if (cooldownTtl > 0) {
            long hours = cooldownTtl / 3600;
            long minutes = (cooldownTtl % 3600) / 60;
            String timeMsg = hours > 0 ? hours + " hours " + minutes + " minutes" : minutes + " minutes";
            throw new RuntimeException("Too many failed login attempts. Please try again in " + timeMsg);
        }
    }

    public boolean initPassword(String userId, @Length(min = 8, max = 20) String password) {
        accountService.initPassword(userId, password);
        return true;
    }

    /**
     * 用户注销
     */
    public void signOut() {
        if (StpUtil.isLogin()) {
            StpUtil.logout();
        }
    }

    public String getSession() {
        if (StpUtil.isLogin()) {
            return StpUtil.getLoginIdAsString();
        }
        return null;
    }
}
