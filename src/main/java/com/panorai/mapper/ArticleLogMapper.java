package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.ArticleLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.Instant;

@Mapper
public interface ArticleLogMapper extends BaseMapper<ArticleLog> {
    
    /**
     * 统计文章浏览次数（按日去重，同一用户同一天只算一次浏览）
     */
    @Select("SELECT COUNT(DISTINCT CONCAT(COALESCE(user_id, ip_address::text), DATE(created_at))) FROM article_logs WHERE article_id = #{articleId}")
    int countByArticleId(String articleId);
    
    /**
     * 统计指定时间后的文章浏览次数（按日去重）
     */
    @Select("SELECT COUNT(DISTINCT CONCAT(COALESCE(user_id, ip_address::text), DATE(created_at))) FROM article_logs WHERE article_id = #{articleId} AND created_at > #{afterTime}")
    int countByArticleIdAfterTime(String articleId, Instant afterTime);
    
    /**
     * 统计指定时间区间内的文章浏览次数（按日去重）
     */
    @Select("SELECT COUNT(DISTINCT CONCAT(COALESCE(user_id, ip_address::text), DATE(created_at))) FROM article_logs WHERE article_id = #{articleId} AND created_at > #{startTime} AND created_at <= #{endTime}")
    int countByArticleIdBetweenTime(String articleId, Instant startTime, Instant endTime);
} 