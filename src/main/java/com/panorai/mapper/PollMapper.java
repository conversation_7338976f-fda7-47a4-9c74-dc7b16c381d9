package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.Poll;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.Instant;
import java.util.List;

@Mapper
public interface PollMapper extends BaseMapper<Poll> {
    
    /**
     * 查找活跃的投票列表（未删除且未过期）
     */
    @Select("SELECT * FROM polls WHERE deleted_at IS NULL AND status = 'active' " +
            "AND (expires_at IS NULL OR expires_at > NOW()) " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<Poll> findActivePolls(@Param("limit") int limit);
    
    /**
     * 根据作者ID查找投票列表
     */
    @Select("SELECT * FROM polls WHERE author_id = #{authorId} AND deleted_at IS NULL " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<Poll> findByAuthorId(@Param("authorId") String authorId, @Param("limit") int limit);
    
    /**
     * 查找已过期但状态仍为active的投票
     */
    @Select("SELECT * FROM polls WHERE status = 'active' AND expires_at IS NOT NULL " +
            "AND expires_at <= #{currentTime} AND deleted_at IS NULL")
    List<Poll> findExpiredActivePolls(@Param("currentTime") Instant currentTime);
    
    /**
     * 根据可见性查找公开投票
     */
    @Select("SELECT * FROM polls WHERE visibility = #{visibility} AND deleted_at IS NULL " +
            "AND status = 'active' ORDER BY created_at DESC LIMIT #{limit}")
    List<Poll> findByVisibility(@Param("visibility") String visibility, @Param("limit") int limit);
}
