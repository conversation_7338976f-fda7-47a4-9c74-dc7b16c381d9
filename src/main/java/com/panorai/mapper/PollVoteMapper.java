package com.panorai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.panorai.model.entity.PollVote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PollVoteMapper extends BaseMapper<PollVote> {
    
    /**
     * 统计投票的总票数
     */
    @Select("SELECT COUNT(*) FROM poll_votes WHERE poll_id = #{pollId}")
    long countByPollId(@Param("pollId") String pollId);
    
    /**
     * 统计投票的参与人数
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM poll_votes WHERE poll_id = #{pollId}")
    long countParticipantsByPollId(@Param("pollId") String pollId);
    
    /**
     * 统计某个选项的票数
     */
    @Select("SELECT COUNT(*) FROM poll_votes WHERE poll_id = #{pollId} AND option_id = #{optionId}")
    long countByPollIdAndOptionId(@Param("pollId") String pollId, @Param("optionId") String optionId);
    
    /**
     * 查找用户在某个投票中的所有投票记录
     */
    @Select("SELECT * FROM poll_votes WHERE poll_id = #{pollId} AND user_id = #{userId}")
    List<PollVote> findByPollIdAndUserId(@Param("pollId") String pollId, @Param("userId") String userId);
    
    /**
     * 查找用户的投票历史
     */
    @Select("SELECT * FROM poll_votes WHERE user_id = #{userId} ORDER BY voted_at DESC LIMIT #{limit}")
    List<PollVote> findByUserId(@Param("userId") String userId, @Param("limit") int limit);
    
    /**
     * 检查用户是否已在某个投票中投票
     */
    @Select("SELECT COUNT(*) > 0 FROM poll_votes WHERE poll_id = #{pollId} AND user_id = #{userId}")
    boolean hasUserVoted(@Param("pollId") String pollId, @Param("userId") String userId);
    
    /**
     * 获取投票的所有选项统计
     */
    @Select("SELECT option_id, COUNT(*) as vote_count FROM poll_votes " +
            "WHERE poll_id = #{pollId} GROUP BY option_id ORDER BY vote_count DESC")
    List<PollVoteStatistic> getVoteStatistics(@Param("pollId") String pollId);
    
    /**
     * 投票统计结果内部类
     */
    class PollVoteStatistic {
        private String optionId;
        private Long voteCount;
        
        public String getOptionId() { return optionId; }
        public void setOptionId(String optionId) { this.optionId = optionId; }
        public Long getVoteCount() { return voteCount; }
        public void setVoteCount(Long voteCount) { this.voteCount = voteCount; }
    }
}
