package com.panorai.model.enums;

import lombok.Getter;

/**
 * 投票状态枚举
 */
@Getter
public enum PollStatus {
    ACTIVE("active", "进行中"),
    CLOSED("closed", "已关闭"),
    EXPIRED("expired", "已过期"),
    DRAFT("draft", "草稿");
    
    private final String code;
    private final String description;
    
    PollStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PollStatus fromCode(String code) {
        for (PollStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return ACTIVE; // 默认返回进行中
    }
}
