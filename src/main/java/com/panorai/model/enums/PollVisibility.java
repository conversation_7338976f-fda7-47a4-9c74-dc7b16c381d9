package com.panorai.model.enums;

import lombok.Getter;

/**
 * 投票可见性枚举
 */
@Getter
public enum PollVisibility {
    PUBLIC("public", "公开"),
    PRIVATE("private", "私有"),
    ONLY_LOGGED_IN("onlyLoggedIn", "仅登录用户可见");
    
    private final String code;
    private final String description;
    
    PollVisibility(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PollVisibility fromCode(String code) {
        for (PollVisibility visibility : values()) {
            if (visibility.code.equals(code)) {
                return visibility;
            }
        }
        return PUBLIC; // 默认返回公开
    }
}
