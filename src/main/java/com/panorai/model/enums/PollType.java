package com.panorai.model.enums;

import lombok.Getter;

/**
 * 投票类型枚举
 */
@Getter
public enum PollType {
    SINGLE("single", "单选投票"),
    MULTIPLE("multiple", "多选投票");
    
    private final String code;
    private final String description;
    
    PollType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据code获取枚举
     */
    public static PollType fromCode(String code) {
        for (PollType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return SINGLE; // 默认返回单选
    }
}
