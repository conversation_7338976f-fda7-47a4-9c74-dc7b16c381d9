package com.panorai.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 内容互动数据DTO
 */
@Data
@Builder
public class ContentMetricsDTO {
    private String contentId;
    private String authorId;
    private int likeCount;
    private int commentCount;
    private int viewCount;
    private int shareCount;
    private int uniqueViewers;
    private double avgReadTime;
    private double bounceRate;
    private String contentType;
    private boolean isFeatured;
} 