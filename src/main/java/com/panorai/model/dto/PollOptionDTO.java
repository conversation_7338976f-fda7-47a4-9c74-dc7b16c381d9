package com.panorai.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 投票选项DTO
 * 用于JSONB字段的序列化和反序列化
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PollOptionDTO {
    /**
     * 选项ID
     */
    private String optionId;
    
    /**
     * 选项文本
     */
    private String text;
    
    /**
     * 选项图片URL（可选）
     */
    private String imageUrl;
    
    /**
     * 选项排序
     */
    private Integer order;
}
