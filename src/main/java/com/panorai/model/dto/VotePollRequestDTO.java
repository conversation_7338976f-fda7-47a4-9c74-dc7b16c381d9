package com.panorai.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 投票请求DTO
 */
@Data
@NoArgsConstructor
public class VotePollRequestDTO {
    /**
     * 投票ID
     */
    @NotBlank(message = "投票ID不能为空")
    private String pollId;
    
    /**
     * 选择的选项ID列表
     * 单选投票时只能包含一个选项ID
     * 多选投票时可以包含多个选项ID
     */
    @NotEmpty(message = "必须选择至少一个选项")
    private List<String> optionIds;
}
