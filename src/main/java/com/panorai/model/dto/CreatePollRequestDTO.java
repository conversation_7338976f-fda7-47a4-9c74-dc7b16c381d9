package com.panorai.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.Instant;
import java.util.List;

/**
 * 创建投票请求DTO
 */
@Data
@NoArgsConstructor
public class CreatePollRequestDTO {
    /**
     * 投票标题
     */
    @NotBlank(message = "投票标题不能为空")
    @Size(max = 200, message = "投票标题不能超过200个字符")
    private String title;
    
    /**
     * 投票描述
     */
    @Size(max = 1000, message = "投票描述不能超过1000个字符")
    private String description;
    
    /**
     * 投票选项
     */
    @NotEmpty(message = "投票选项不能为空")
    @Size(min = 2, max = 10, message = "投票选项数量必须在2-10个之间")
    private List<CreatePollOptionDTO> options;
    
    /**
     * 投票类型：single-单选，multiple-多选
     */
    private String pollType = "single";
    
    /**
     * 过期时间
     */
    private Instant expiresAt;
    
    /**
     * 是否允许匿名投票
     */
    private Boolean allowAnonymous = false;
    
    /**
     * 可见性：public-公开，private-私有，onlyLoggedIn-仅登录用户可见
     */
    private String visibility = "public";
    
    /**
     * 创建投票选项DTO
     */
    @Data
    @NoArgsConstructor
    public static class CreatePollOptionDTO {
        /**
         * 选项文本
         */
        @NotBlank(message = "选项文本不能为空")
        @Size(max = 100, message = "选项文本不能超过100个字符")
        private String text;
        
        /**
         * 选项图片URL（可选）
         */
        private String imageUrl;
        
        /**
         * 选项排序
         */
        private Integer order;
    }
}
