package com.panorai.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 投票结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PollResultDTO {
    /**
     * 投票ID
     */
    private String pollId;
    
    /**
     * 投票标题
     */
    private String title;
    
    /**
     * 投票描述
     */
    private String description;
    
    /**
     * 投票类型
     */
    private String pollType;
    
    /**
     * 投票状态
     */
    private String status;
    
    /**
     * 总投票数
     */
    private Long totalVotes;
    
    /**
     * 总参与人数
     */
    private Long totalParticipants;
    
    /**
     * 选项结果列表
     */
    private List<PollOptionResultDTO> optionResults;
    
    /**
     * 当前用户是否已投票
     */
    private Boolean hasVoted;
    
    /**
     * 当前用户投票的选项ID列表
     */
    private List<String> userVotedOptions;
    
    /**
     * 投票选项结果DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PollOptionResultDTO {
        /**
         * 选项ID
         */
        private String optionId;
        
        /**
         * 选项文本
         */
        private String text;
        
        /**
         * 选项图片URL
         */
        private String imageUrl;
        
        /**
         * 投票数
         */
        private Long voteCount;
        
        /**
         * 投票百分比
         */
        private Double percentage;
        
        /**
         * 排序
         */
        private Integer order;
    }
}
