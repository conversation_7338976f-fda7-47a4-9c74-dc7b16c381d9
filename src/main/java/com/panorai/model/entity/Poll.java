package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.panorai.model.dto.PollOptionDTO;
import com.panorai.model.enums.PollStatus;
import com.panorai.model.enums.PollType;
import com.panorai.model.enums.PollVisibility;
import com.panorai.utils.IdUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * 投票实体类
 */
@Data
@TableName("polls")
@NoArgsConstructor
public class Poll {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 投票唯一标识
     */
    private String pollId;
    
    /**
     * 投票标题
     */
    private String title;
    
    /**
     * 投票描述
     */
    private String description;
    
    /**
     * 创建者ID
     */
    private String authorId;
    
    /**
     * 投票选项（JSON格式存储）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<PollOptionDTO> options;
    
    /**
     * 投票类型：single-单选，multiple-多选
     */
    private String pollType = PollType.SINGLE.getCode();
    
    /**
     * 投票状态：active-进行中，closed-已关闭，expired-已过期，draft-草稿
     */
    private String status = PollStatus.ACTIVE.getCode();
    
    /**
     * 过期时间
     */
    private Instant expiresAt;
    
    /**
     * 是否允许匿名投票
     */
    private Boolean allowAnonymous = false;
    
    /**
     * 可见性：public-公开，private-私有，onlyLoggedIn-仅登录用户可见
     */
    private String visibility = PollVisibility.PUBLIC.getCode();
    
    /**
     * 创建时间
     */
    private Instant createdAt = Instant.now();
    
    /**
     * 更新时间
     */
    private Instant updatedAt = Instant.now();
    
    /**
     * 删除时间（软删除）
     */
    private Instant deletedAt;
    
    /**
     * 构造函数
     */
    public Poll(String title, String description, String authorId, List<PollOptionDTO> options) {
        this.pollId = IdUtils.generateId();
        this.title = title;
        this.description = description;
        this.authorId = authorId;
        this.options = options;
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
    
    /**
     * 检查投票是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && Instant.now().isAfter(expiresAt);
    }
    
    /**
     * 检查投票是否处于活跃状态
     */
    public boolean isActive() {
        return PollStatus.ACTIVE.getCode().equals(status) && !isExpired();
    }
    
    /**
     * 关闭投票
     */
    public void close() {
        this.status = PollStatus.CLOSED.getCode();
        this.updatedAt = Instant.now();
    }
    
    /**
     * 设置过期状态
     */
    public void expire() {
        this.status = PollStatus.EXPIRED.getCode();
        this.updatedAt = Instant.now();
    }
}
