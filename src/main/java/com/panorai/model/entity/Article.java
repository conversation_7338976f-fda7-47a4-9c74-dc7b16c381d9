package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.panorai.model.enums.ArticleContentType;
import com.panorai.model.enums.ArticleVisibility;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("articles")
@NoArgsConstructor
public class Article {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String articleId;
    
    private String title;
    
    private String content;
    
    private String authorId;
    
    private String coverImage;
    
    private String summary;
    
    private String[] tags;
    
    private Instant createdAt;
    
    private Instant updatedAt;
    
    private String visibility = ArticleVisibility.PUBLIC.getCode();
    
    private Boolean isHidden = false;
    
    private String warningText;
    
    private Instant deletedAt;

    private String contentType = ArticleContentType.ORIGINAL.getCode();
    
    private String originalAuthor;
    
    private String originalSource;
    
    private String translationPermission;
    
    private Boolean applyForIncentive = false;

    private Boolean isFeatured = false;
} 