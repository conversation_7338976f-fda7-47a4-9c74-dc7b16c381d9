package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@TableName("article_comments")
@NoArgsConstructor
public class ArticleComment {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String commentId;
    
    private String content;
    
    private String authorId;
    
    private String articleId;
    
    private String parentId;
    
    private String rootCommentId;
    
    private Instant createdAt;
    
    private Instant updatedAt;
    
    private Instant deletedAt;
} 