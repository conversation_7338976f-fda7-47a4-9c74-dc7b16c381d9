package com.panorai.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * 投票记录实体类
 */
@Data
@TableName("poll_votes")
@NoArgsConstructor
public class PollVote {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 投票ID
     */
    private String pollId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 选项ID
     */
    private String optionId;
    
    /**
     * 投票时间
     */
    private Instant votedAt = Instant.now();
    
    /**
     * 创建时间
     */
    private Instant createdAt = Instant.now();
    
    /**
     * 更新时间
     */
    private Instant updatedAt = Instant.now();
    
    /**
     * 构造函数
     */
    public PollVote(String pollId, String userId, String optionId) {
        this.pollId = pollId;
        this.userId = userId;
        this.optionId = optionId;
        this.votedAt = Instant.now();
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
}
