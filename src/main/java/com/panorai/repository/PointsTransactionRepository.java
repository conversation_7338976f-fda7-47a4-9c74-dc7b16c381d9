package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.PointsTransaction;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 积分交易记录Repository接口
 */
public interface PointsTransactionRepository extends IService<PointsTransaction> {
    /**
     * 根据用户ID查找交易记录
     */
    List<PointsTransaction> findByUserId(String userId, int limit);
    
    /**
     * 根据用户ID和交易类型查找记录
     */
    List<PointsTransaction> findByUserIdAndType(String userId, String type, int limit);
    
    /**
     * 根据时间范围查找交易记录
     */
    List<PointsTransaction> findByCreatedAtBetween(Instant startTime, Instant endTime);
    
    /**
     * 统计用户指定类型的积分总量
     */
    long sumAmountByUserIdAndType(String userId, String type);
    
    /**
     * 根据内容ID查找相关交易
     */
    List<PointsTransaction> findByContentId(String contentId);
    
    /**
     * 查找内容的最后一次互动积分交易记录
     */
    List<PointsTransaction> findLastContentInteractionTransaction(String contentId);
    
    /**
     * 根据内容ID和交易子类型查找记录
     */
    List<PointsTransaction> findByContentIdAndSubType(String contentId, String subType);
    
    /**
     * 计算指定内容的总积分
     */
    long sumPointsByContentId(String contentId);
} 