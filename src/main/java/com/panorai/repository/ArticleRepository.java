package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.Article;

import java.util.List;
import java.util.Optional;

public interface ArticleRepository extends IService<Article> {
    
    /**
     * 根据文章ID查找文章
     */
    Optional<Article> findByArticleId(String articleId);
    
    /**
     * 获取活跃内容ID列表（供积分计算使用）
     */
    List<String> findActiveContentIds();

    List<Article> findActiveContentWithOutContent();
} 