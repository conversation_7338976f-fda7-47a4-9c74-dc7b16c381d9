package com.panorai.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.panorai.model.entity.CreatorPoints;

import java.util.List;

/**
 * 创作者积分账户Repository接口
 */
public interface CreatorPointsRepository extends IService<CreatorPoints> {
    
    /**
     * 根据用户ID查找积分账户
     */
    CreatorPoints findByUserId(String userId);
    
    /**
     * 查找总积分排行榜
     */
    List<CreatorPoints> findTopByTotalPoints(int limit);
    
    /**
     * 查找可提现积分大于指定值的账户
     */
    List<CreatorPoints> findByAvailablePointsGreaterThan(long points);
    
    /**
     * 统计总的积分账户数
     */
    long countAll();
    
    /**
     * 统计总的积分发放量
     */
    long sumTotalPoints();
} 