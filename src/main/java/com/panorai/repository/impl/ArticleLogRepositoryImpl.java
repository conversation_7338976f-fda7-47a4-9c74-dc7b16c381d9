package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.ArticleLogMapper;
import com.panorai.model.entity.ArticleLog;
import com.panorai.repository.ArticleLogRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@Repository
public class ArticleLogRepositoryImpl extends ServiceImpl<ArticleLogMapper, ArticleLog> implements ArticleLogRepository {

    @Override
    public int countByArticleId(String articleId) {
        return baseMapper.countByArticleId(articleId);
    }

    @Override
    public int countByArticleIdAfterTime(String articleId, Instant afterTime) {
        return baseMapper.countByArticleIdAfterTime(articleId, afterTime);
    }

    @Override
    public int countByArticleIdBetweenTime(String articleId, Instant startTime, Instant endTime) {
        return baseMapper.countByArticleIdBetweenTime(articleId, startTime, endTime);
    }
} 