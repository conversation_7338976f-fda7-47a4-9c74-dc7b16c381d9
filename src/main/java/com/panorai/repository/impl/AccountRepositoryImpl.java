package com.panorai.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.panorai.mapper.AccountMapper;
import com.panorai.model.entity.Account;
import com.panorai.repository.AccountRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class AccountRepositoryImpl extends ServiceImpl<AccountMapper, Account> implements AccountRepository {
    @Override
    public Optional<Account> optByUserId(String userId) {
        return this.lambdaQuery()
                .eq(Account::getUserId, userId)
                .oneOpt();
    }
}