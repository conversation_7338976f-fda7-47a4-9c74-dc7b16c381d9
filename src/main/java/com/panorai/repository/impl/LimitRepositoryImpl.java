package com.panorai.repository.impl;

import com.panorai.repository.LimitRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Repository
@RequiredArgsConstructor
public class LimitRepositoryImpl implements LimitRepository {
    private final StringRedisTemplate redisTemplate;
    private static final String IP_LIMIT_KEY_PREFIX = "limit:opt:ip:";
    private static final String EMAIL_LIMIT_KEY_PREFIX = "limit:opt:email:";
    private static final String SIGN_IN_FAIL_COUNT_KEY_PREFIX = "limit:signin:fail:";

    private static final int SIGN_IN_COOLDOWN_SECONDS = 60 * 60 * 12;

    @Override
    public long checkIpLimit(String ipAddress) {
        String key = IP_LIMIT_KEY_PREFIX + ipAddress;
        return getTtl(key);
    }

    @Override
    public long checkEmailLimit(String email) {
        String key = EMAIL_LIMIT_KEY_PREFIX + email;
        return getTtl(key);
    }

    @Override
    public void setIpLimit(String ipAddress) {
        String key = IP_LIMIT_KEY_PREFIX + ipAddress;
        tryAcquire(key, 60);
    }

    @Override
    public void setEmailLimit(String email) {
        String key = EMAIL_LIMIT_KEY_PREFIX + email;
        tryAcquire(key, 60);
    }

    private long getTtl(String key) {
        Boolean existed = redisTemplate.hasKey(key);
        if (!existed) {
            return -2;
        }
        return redisTemplate.getExpire(key);
    }

    public void tryAcquire(String key, int seconds) {
        redisTemplate.opsForValue().setIfAbsent(key, "1", seconds, TimeUnit.SECONDS);
    }

    @Override
    public int incrementSignInFailCount(String userId) {
        String key = SIGN_IN_FAIL_COUNT_KEY_PREFIX + userId;

        Long count = redisTemplate.opsForValue().increment(key);
        count = Optional.ofNullable(count).orElse(0L);

        if (count == 1) {
            redisTemplate.expire(key, SIGN_IN_COOLDOWN_SECONDS, TimeUnit.SECONDS);
        }

        return count.intValue();
    }

    @Override
    public void clearSignInFailCount(String userId) {
        String failCountKey = SIGN_IN_FAIL_COUNT_KEY_PREFIX + userId;
        redisTemplate.delete(failCountKey);
    }

    @Override
    public int getSignInFailCount(String userId) {
        String key = SIGN_IN_FAIL_COUNT_KEY_PREFIX + userId;
        String count = redisTemplate.opsForValue().get(key);
        return count != null ? Integer.parseInt(count) : 0;
    }
}
