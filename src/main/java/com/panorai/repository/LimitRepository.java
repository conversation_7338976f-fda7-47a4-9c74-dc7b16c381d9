package com.panorai.repository;

public interface LimitRepository {
    long checkIpLimit(String ipAddress);

    long checkEmailLimit(String email);

    void setIpLimit(String ipAddress);

    void setEmailLimit(String email);

    /**
     * 增加用户登录失败次数
     * @param userId 用户ID
     * @return 当前失败次数
     */
    int incrementSignInFailCount(String userId);

    /**
     * 清除用户登录失败记录（登录成功时调用）
     * @param userId 用户ID
     */
    void clearSignInFailCount(String userId);

    /**
     * 获取用户当前登录失败次数
     * @param userId 用户ID
     * @return 失败次数
     */
    int getSignInFailCount(String userId);
}
